package com.hwb.timecontroller.business

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.widget.Toast
import com.hwb.timecontroller.BuildConfig
import com.hwb.timecontroller.MyApplication
import com.hwb.timecontroller.network.NetworkConfig
import com.hwb.timecontroller.network.NetworkManager
import com.hwb.timecontroller.network.BalanceTimeResponse
import com.hwb.timecontroller.network.DeductMoneyResponse
import com.hwb.timecontroller.service.CountdownService
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.http.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.elvishew.xlog.XLog

/**
 * 余额轮询管理器
 * 负责用户登录后的余额查询长轮询服务和倒计时同步
 * 
 * Author: huangwubin
 * Date: 2025/7/11
 */
object BalancePollingManager : UserLoginStatusListener {

    private const val TAG = "BalancePollingManager"

    // 轮询间隔（5分钟，用于定期获取最新余额并刷新比例）
    private const val POLLING_INTERVAL_MS = 5 * 60_000L

    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 应用上下文（用于启动服务）
    private var applicationContext: Context? = null
    
    // 轮询任务
    private var pollingJob: Job? = null
    
    // 轮询状态
    private val _isPollingActive = MutableStateFlow(false)
    val isPollingActive: StateFlow<Boolean> = _isPollingActive.asStateFlow()
    
    // 最后一次余额查询结果
    private val _lastBalanceData = MutableStateFlow<com.hwb.timecontroller.network.ClientLoginData?>(null)
    val lastBalanceData: StateFlow<com.hwb.timecontroller.network.ClientLoginData?> = _lastBalanceData.asStateFlow()
    
    // 轮询错误状态
    private val _pollingError = MutableStateFlow<String?>(null)
    val pollingError: StateFlow<String?> = _pollingError.asStateFlow()

    // 扣款状态
    private val _isDeducting = MutableStateFlow(false)
    val isDeducting: StateFlow<Boolean> = _isDeducting.asStateFlow()

    // 最后一次扣款结果
    private val _lastDeductionResult = MutableStateFlow<Result<DeductMoneyResponse>?>(null)
    val lastDeductionResult: StateFlow<Result<DeductMoneyResponse>?> = _lastDeductionResult.asStateFlow()
    
    /**
     * 初始化余额轮询管理器
     */
    fun initialize() {
        try {
            // 保存应用上下文
            applicationContext = MyApplication.myApp

            // 注册为用户登录状态监听器
            UserManager.addLoginStatusListener(this)

            // 如果用户已经登录，启动轮询
            if (UserManager.isUserLoggedIn()) {
                startPolling()
            }

            XLog.d("余额轮询管理器初始化完成")
        } catch (e: Exception) {
            XLog.e("余额轮询管理器初始化失败", e)
        }
    }
    
    /**
     * 启动余额轮询
     */
    private fun startPolling() {
        if (_isPollingActive.value) {
            XLog.d("余额轮询已在运行，忽略重复启动")
            return
        }
        
        XLog.d("启动余额轮询服务（5分钟间隔，用于定期刷新比例）")
        _isPollingActive.value = true
        _pollingError.value = null
        
        pollingJob = managerScope.launch {
            try {
                while (_isPollingActive.value && UserManager.isUserLoggedIn()) {
                    try {
                        // 执行余额查询
                        queryBalance()
                        
                        // 等待下一次轮询
                        delay(POLLING_INTERVAL_MS)
                        
                    } catch (e: Exception) {
                        XLog.e("余额轮询执行异常，继续轮询", e)
                        _pollingError.value = "网络请求失败: ${e.message}"
                        
                        // 出错后等待较短时间再重试
                        delay(10_000L)
                    }
                }
            } catch (e: Exception) {
                XLog.e("余额轮询协程异常", e)
            } finally {
                _isPollingActive.value = false
                XLog.d("余额轮询服务已停止")
            }
        }
    }
    
    /**
     * 停止余额轮询
     */
    private fun stopPolling() {
        XLog.d("停止余额轮询服务")
        
        _isPollingActive.value = false
        pollingJob?.cancel()
        pollingJob = null
        
        // 清除错误状态
        _pollingError.value = null
    }
    
    /**
     * 执行余额查询
     * @param syncCountdown 是否同步倒计时，默认为true
     */
    private suspend fun queryBalance(syncCountdown: Boolean = true) {
        try {
            // 获取当前登录用户的ID
            val currentUserInfo = UserManager.getUserInfo()
            val userId = currentUserInfo?.userId

            if (userId.isNullOrEmpty()) {
                XLog.w("用户ID为空，无法查询余额")
                _pollingError.value = "用户ID为空"
                return
            }

            val fullUrl = NetworkConfig.BASE_URL + NetworkConfig.BASE_PATH + "/" + NetworkConfig.ENDPOINT_QUERY_BALANCE

            XLog.d("执行余额查询: $fullUrl, userId: $userId")

            val response = NetworkManager.httpClient.get(fullUrl) {
                parameter("userId", userId)
            }
            
            if (response.status == HttpStatusCode.OK) {
                val balanceResponse = response.body<BalanceTimeResponse>()
                XLog.d("余额查询API响应: code=${balanceResponse.code}, msg=${balanceResponse.msg}, rspdata=${balanceResponse.rspdata}")
                handleBalanceTimeResponse(balanceResponse, syncCountdown)
            } else {
                XLog.w("余额查询请求失败: ${response.status}")
                _pollingError.value = "服务器响应错误: ${response.status}"
            }
            
        } catch (e: Exception) {
            XLog.e("余额查询异常", e)
            _pollingError.value = "查询余额失败: ${e.message}"
            throw e
        }
    }
    
    /**
     * 处理余额时间查询响应
     * @param response 响应数据
     * @param syncCountdown 是否同步倒计时
     */
    private fun handleBalanceTimeResponse(response: BalanceTimeResponse, syncCountdown: Boolean = true) {
        try {
            XLog.d("开始处理余额时间查询响应: ${response}")

            // 检查响应码
            if (response.code != 200) {
                XLog.w("${response.msg}，余额查询API返回错误: code=${response.code}")
                _pollingError.value = "服务器错误: ${response.msg}"
                return
            }

            val balanceTimeData = response.rspdata
            if (balanceTimeData != null) {
                // 从新接口获取时间（分钟）和余额
                val timeInMinutes = balanceTimeData.timeInMinutes
                val balance = balanceTimeData.balance

                // 转换为秒
                val tempTimeSeconds = timeInMinutes * 60

                // 创建兼容的ClientLoginData对象
                val compatibleBalanceData = com.hwb.timecontroller.network.ClientLoginData(
                    money = balance,
                    temp_time = tempTimeSeconds
                )

                XLog.d("余额时间查询成功: balance=${balance}, timeInMinutes=${timeInMinutes}, tempTimeSeconds=${tempTimeSeconds}")

                // 更新余额数据
                _lastBalanceData.value = compatibleBalanceData

                // 更新UserManager中的客户端登录数据
                val currentClientData = UserManager.getClientLoginData()
                if (currentClientData != null) {
                    val updatedClientData = currentClientData.copy(
                        money = balance,
                        temp_time = tempTimeSeconds
                    )
                    UserManager.setClientLoginData(updatedClientData)
                }

                // 根据参数决定是否同步倒计时时间
                if (syncCountdown) {
                    val tempTimeMillis = tempTimeSeconds * 1000L
                    XLog.d("同步倒计时时间: ${tempTimeSeconds}秒 (${tempTimeMillis}毫秒)")

                    if (tempTimeMillis > 0) {
                        // 检查倒计时是否正在运行
                        if (CountdownManager.isCountdownRunning()) {
                            // 如果倒计时正在运行，直接更新剩余时间
                            // CountdownService会通过observeCountdownState检测到变化并自动同步
                            XLog.d("倒计时正在运行，更新剩余时间: ${tempTimeMillis}毫秒")
                            CountdownManager.updateRemainingTime(tempTimeMillis)
                        } else {
                            // 如果倒计时没有运行，启动新的倒计时服务
                            XLog.d("倒计时未运行，启动新的倒计时服务: ${tempTimeMillis}毫秒")
                            startCountdownService(tempTimeMillis)
                        }
                    } else {
                        // 如果时间为0，只更新CountdownManager，表示倒计时结束
                        CountdownManager.updateRemainingTime(tempTimeMillis)
                    }
                } else {
                    XLog.d("跳过倒计时同步，仅更新余额数据")
                }

                // 清除错误状态
                _pollingError.value = null

            } else {
                XLog.w("余额时间查询响应数据为空，可能是服务器错误")
                _pollingError.value = "服务器返回数据为空"
            }

        } catch (e: Exception) {
            XLog.e("处理余额时间查询响应失败", e)
            _pollingError.value = "处理响应数据失败: ${e.message}"
        }
    }
    
    /**
     * 启动倒计时服务
     * @param durationMillis 倒计时时长（毫秒）
     */
    private fun startCountdownService(durationMillis: Long) {
        try {
            val context = applicationContext
            if (context == null) {
                XLog.w("应用上下文为空，无法启动倒计时服务")
                return
            }

            XLog.d("启动倒计时服务: ${durationMillis}毫秒")

            val intent = Intent(context, CountdownService::class.java)
            intent.putExtra("duration", durationMillis)
            context.startForegroundService(intent)

            XLog.d("倒计时服务启动成功")

        } catch (e: Exception) {
            XLog.e("启动倒计时服务失败", e)
        }
    }

    /**
     * 手动刷新余额（用于用户主动刷新）
     */
    suspend fun refreshBalance(): Result<com.hwb.timecontroller.network.ClientLoginData?> {
        return try {
            if (!UserManager.isUserLoggedIn()) {
                Result.failure(Exception("用户未登录"))
            } else {
                queryBalance()
                Result.success(_lastBalanceData.value)
            }
        } catch (e: Exception) {
            XLog.e("手动刷新余额失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 实现UserLoginStatusListener接口
     */
    override fun onLoginStatusChanged(isLoggedIn: Boolean) {
        XLog.d("用户登录状态变化: $isLoggedIn")
        
        if (isLoggedIn) {
            // 用户登录，启动轮询
            startPolling()
        } else {
            // 用户退出登录，停止轮询
            stopPolling()
            
            // 清除余额数据
            _lastBalanceData.value = null
        }
    }
    
    /**
     * 执行扣款操作
     * @param userId 用户ID
     * @param amount 扣款金额
     * @param autoRefreshBalance 是否自动刷新余额，默认true
     * @return 扣款结果
     */
    @SuppressLint("DefaultLocale")
    suspend fun deductMoney(userId: String, amount: Double, autoRefreshBalance: Boolean = true): Result<DeductMoneyResponse> {
        return withContext(Dispatchers.IO) {
            try {
                if (_isDeducting.value) {
                    return@withContext Result.failure(Exception("扣款操作正在进行中，请稍后再试"))
                }

                _isDeducting.value = true

                // Debug模式下按N倍费率扣款
                val actualAmount = if (BuildConfig.DEBUG) {
                    val debugAmount = amount * 1
                    XLog.d("Debug模式: 原始金额=$amount, 实际扣款金额=$debugAmount (10倍费率)")

                    debugAmount
                } else {
                    amount
                }

                XLog.d("开始执行扣款操作: userId=$userId, 请求金额=$amount, 实际扣款金额=$actualAmount")

                val fullUrl = NetworkConfig.BASE_URL + NetworkConfig.BASE_PATH + "/" + NetworkConfig.ENDPOINT_DEDUCT_MONEY

                val response = NetworkManager.httpClient.post(fullUrl) {
                    setBody(FormDataContent(Parameters.build {
                        append("userId", userId)
                        append("amount", actualAmount.toString())
                    }))
                }

                if (response.status == HttpStatusCode.OK) {
                    val deductResponse = response.body<DeductMoneyResponse>()
                    XLog.d("扣款API响应: code=${deductResponse.code}, msg=${deductResponse.msg}, rspdata=${deductResponse.rspdata}")

                    val result = Result.success(deductResponse)
                    _lastDeductionResult.value = result

                    // 如果扣款成功，根据参数决定是否刷新余额
                    if (deductResponse.code == 200 && deductResponse.rspdata != null) {
                        val deductedAmount = deductResponse.rspdata.deductedAmount ?: actualAmount
                        if (BuildConfig.DEBUG) {
                            XLog.d("Debug模式扣款成功: 请求金额=$amount, 实际扣款=$deductedAmount")
                        } else {
                            XLog.d("扣款成功: 扣款金额=$deductedAmount")
                        }

                        if (autoRefreshBalance) {
                            XLog.d("立即刷新余额并同步倒计时")
                            try {
                                queryBalance(syncCountdown = true)
                            } catch (e: Exception) {
                                XLog.w("扣款后刷新余额失败", e)
                            }
                        } else {
                            XLog.d("跳过自动余额刷新，由调用方处理")
                        }
                    } else if (deductResponse.code == 300) {
                        XLog.w("扣款失败 - 余额不足: ${deductResponse.msg}")
                    }

                    result
                } else {
                    val errorMsg = "扣款请求失败: ${response.status}"
                    XLog.w(errorMsg)
                    val result = Result.failure<DeductMoneyResponse>(Exception(errorMsg))
                    _lastDeductionResult.value = result
                    result
                }

            } catch (e: Exception) {
                XLog.e("扣款操作异常", e)
                val result = Result.failure<DeductMoneyResponse>(e)
                _lastDeductionResult.value = result
                result
            } finally {
                _isDeducting.value = false
            }
        }
    }

    /**
     * 扣款操作（异步）
     * @param userId 用户ID
     * @param amount 扣款金额
     * @param onResult 结果回调
     */
    fun deductMoneyAsync(
        userId: String,
        amount: Double,
        onResult: (Result<DeductMoneyResponse>) -> Unit
    ) {
        managerScope.launch {
            val result = deductMoney(userId, amount)
            withContext(Dispatchers.Main) {
                onResult(result)
            }
        }
    }

    /**
     * 清除最后一次扣款结果
     */
    fun clearLastDeductionResult() {
        _lastDeductionResult.value = null
    }

    /**
     * 获取每分钟的扣款金额（基于上次换算比例）
     * @return 每分钟费用，如果无法计算返回null
     */
    fun getPerMinuteCost(): Double? {
        val balanceData = _lastBalanceData.value ?: return null
        val balance = balanceData.money ?: return null
        val timeInSeconds = balanceData.temp_time ?: return null

        if (timeInSeconds <= 0) return null

        val timeInMinutes = timeInSeconds / 60.0
        return if (timeInMinutes > 0) balance / timeInMinutes else null
    }

    /**
     * 获取当前余额
     * @return 当前余额，如果无法获取返回null
     */
    fun getCurrentBalance(): Double? {
        return _lastBalanceData.value?.money
    }

    /**
     * 检查余额是否足够指定分钟数的游玩时间
     * @param minutes 游玩分钟数
     * @return 是否足够，如果无法计算返回false
     */
    fun isBalanceSufficientForMinutes(minutes: Double): Boolean {
        val currentBalance = getCurrentBalance() ?: return false
        val perMinuteCost = getPerMinuteCost() ?: return false

        val requiredBalance = perMinuteCost * minutes
        return currentBalance >= requiredBalance
    }

    /**
     * 更新余额缓存数据（用于扣款后直接更新，避免额外API调用）
     * @param newBalance 新余额
     * @param newTimeInSeconds 新的剩余时间（秒）
     */
    fun updateBalanceCache(newBalance: Double, newTimeInSeconds: Int) {
        try {
            val updatedBalanceData = com.hwb.timecontroller.network.ClientLoginData(
                money = newBalance,
                temp_time = newTimeInSeconds
            )

            _lastBalanceData.value = updatedBalanceData
            XLog.d("余额缓存已更新: balance=$newBalance, time=$newTimeInSeconds")

            // 同时更新UserManager中的客户端登录数据
            val currentClientData = UserManager.getClientLoginData()
            if (currentClientData != null) {
                val updatedClientData = currentClientData.copy(
                    money = newBalance,
                    temp_time = newTimeInSeconds
                )
                UserManager.setClientLoginData(updatedClientData)
            }

        } catch (e: Exception) {
            XLog.e("更新余额缓存失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            stopPolling()
            UserManager.removeLoginStatusListener(this)
            managerScope.cancel()
            _isDeducting.value = false
            _lastDeductionResult.value = null
            XLog.d("余额轮询管理器资源清理完成")
        } catch (e: Exception) {
            XLog.e("余额轮询管理器资源清理失败", e)
        }
    }
}
